import time
from datetime import datetime
from DrissionPage import ChromiumPage, ChromiumOptions

def find_and_click_button(driver: ChromiumPage):
    """查找并点击验证按钮"""

    # 方法1: 查找 turnstile 隐藏 input，通过 shadow root 定位按钮
    try:
        eles = driver.eles("tag:input")
        for ele in eles:
            if "name" in ele.attrs.keys() and "type" in ele.attrs.keys():
                if "turnstile" in ele.attrs["name"] and ele.attrs["type"] == "hidden":
                    try:
                        button = ele.parent().shadow_root.child()("tag:body").shadow_root("tag:input")
                        if button:
                            button.click()
                            return True
                    except:
                        pass  # 静默处理，尝试其他方法
                    break
    except:
        pass  # 静默处理，尝试其他方法

    # 方法2: 递归查找 iframe 中的按钮
    try:
        body = driver.ele("tag:body")

        # 递归查找 iframe
        def find_iframe(ele):
            try:
                if ele.shadow_root and ele.shadow_root.child().tag == "iframe":
                    return ele.shadow_root.child()
                for child in ele.children():
                    result = find_iframe(child)
                    if result:
                        return result
            except:
                pass
            return None

        iframe = find_iframe(body)
        if iframe:
            # 在 iframe 中查找 input 按钮
            def find_input(ele):
                try:
                    if ele.shadow_root and ele.shadow_root.ele("tag:input"):
                        return ele.shadow_root.ele("tag:input")
                    for child in ele.children():
                        result = find_input(child)
                        if result:
                            return result
                except:
                    pass
                return None

            button = find_input(iframe("tag:body"))
            if button:
                button.click()
                return True
    except:
        pass  # 静默处理

    return False

def demo_cloudflare_bypass(url):
    print("启动浏览器...")
    try:
        options = ChromiumOptions().auto_port()
        options.ignore_certificate_errors(True)
        options.set_argument("-no-first-run")
        options.set_argument("-force-color-profile=srgb")
        options.set_argument("-metrics-recording-only")
        options.set_argument("-password-store=basic")
        options.set_argument("-use-mock-keychain")
        options.set_argument("-export-tagged-pdf")
        options.set_argument("-no-default-browser-check")
        options.set_argument("-disable-background-mode")
        options.set_argument("-enable-features=NetworkService,NetworkServiceInProcess,LoadCryptoTokenExtension,PermuteTLSExtensions")
        options.set_argument("-disable-features=FlashDeprecationWarning,EnablePasswordsAccountStorage")
        options.set_argument("-deny-permission-prompts")
        options.set_argument("-disable-gpu")
        options.set_argument("-accept-lang=en-US")
        options.set_argument("--window-size=1024,768")
        options.set_argument("--disable-infobars")
        options.set_argument("--window-name=CloudflyerDemo")
        options.set_argument("--disable-sync")
        options.set_argument("--lang=en")
        options.set_retry(20, 0.5)

        driver = ChromiumPage(addr_or_opts=options, timeout=0.5)
        print("浏览器启动成功")
    except Exception as e:
        print(f"浏览器启动失败: {e}")
        return

    print(f"访问: {url}")
    try:
        driver.get(url)
        print(f"页面标题: {driver.title}")
    except Exception as e:
        print(f"页面加载失败: {e}")
        driver.quit()
        return

    # 检查是否需要验证
    def is_challenge_page():
        try:
            return "just a moment" in driver.title.lower()
        except:
            return False

    # 检查是否需要验证
    if is_challenge_page():
        print("开始绕过验证...")
        start_time = datetime.now()
        try_count = 0

        # 主循环：最多尝试20次
        while is_challenge_page() and try_count < 20:
            try_count += 1
            if try_count % 5 == 1:  # 每5次显示一次进度
                print(f"尝试中... ({try_count}/20)")

            find_and_click_button(driver)
            time.sleep(2)

        # 检查结果
        if not is_challenge_page():
            end_time = datetime.now()
            duration = (end_time - start_time).total_seconds()
            print(f"验证成功 ({duration:.1f}s)")
        else:
            # 额外等待
            for _ in range(40):
                if not is_challenge_page():
                    end_time = datetime.now()
                    duration = (end_time - start_time).total_seconds()
                    print(f"验证成功 ({duration:.1f}s)")
                    break
                time.sleep(0.5)
            else:
                end_time = datetime.now()
                duration = (end_time - start_time).total_seconds()
                print(f"验证失败 ({duration:.1f}s)")
    else:
        print("无需验证")

    # 获取关键 cookies
    print("\n获取 cookies:")
    try:
        cookies = driver.cookies()
        cf_clearance = None
        for cookie in cookies:
            if cookie['name'] == 'cf_clearance':
                cf_clearance = cookie['value']
                break

        if cf_clearance:
            print(f"cf_clearance: {cf_clearance}")
        else:
            print("未找到 cf_clearance")
    except Exception as e:
        print(f"获取失败: {e}")

    driver.quit()
    print("完成")

if __name__ == "__main__":
    # demo_cloudflare_bypass('https://search.lionairthai.com/Search.aspx?aid=207&depCity=BKK&arrCity=SUB&Jtype=1&depDate=01%2f07%2f2025&adult1=1&child1=0&infant1=0&promotioncode=&df=UK&afid=0&b2b=0&St=fa&DFlight=false&roomcount=1&ur=')
    # demo_cloudflare_bypass('https://www.potatochipssettlement.com/Claim')
    demo_cloudflare_bypass('https://nopecha.com/demo/cloudflare')
