#!/usr/bin/env python3
"""
测试上游HTTP代理是否可用
"""

import requests
import time

def test_upstream_proxy():
    """直接测试上游代理"""
    upstream_proxy = "http://0465846b31e1f583b17c__cr.us:<EMAIL>:19965"
    
    print("🔍 测试上游HTTP代理...")
    print(f"代理地址: {upstream_proxy}")
    
    proxies = {
        'http': upstream_proxy,
        'https': upstream_proxy
    }
    
    try:
        print("\n📡 测试1: 直接通过上游代理访问httpbin.org...")
        response = requests.get('https://httpbin.org/ip', proxies=proxies, timeout=15)
        if response.status_code == 200:
            ip_info = response.json()
            print(f"✅ 成功！当前IP: {ip_info.get('origin', 'Unknown')}")
        else:
            print(f"❌ 失败！状态码: {response.status_code}")
            
    except requests.exceptions.ProxyError as e:
        print(f"❌ 代理错误: {e}")
    except requests.exceptions.Timeout as e:
        print(f"❌ 超时错误: {e}")
    except requests.exceptions.RequestException as e:
        print(f"❌ 请求错误: {e}")
    except Exception as e:
        print(f"❌ 其他错误: {e}")
    
    try:
        print("\n📡 测试2: HTTPS请求...")
        response = requests.get('https://httpbin.org/ip', timeout=15, verify=False)
        if response.status_code == 200:
            ip_info = response.json()
            print(f"✅ HTTPS成功！当前IP: {ip_info.get('origin', 'Unknown')}")
        else:
            print(f"❌ HTTPS失败！状态码: {response.status_code}")
            
    except Exception as e:
        print(f"❌ HTTPS错误: {e}")

if __name__ == "__main__":
    test_upstream_proxy()
