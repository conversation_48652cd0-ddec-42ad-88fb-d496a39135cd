#!/usr/bin/env python3
"""
测试CONNECT请求
"""

import asyncio
import base64

async def debug_connect_request():
    """测试CONNECT请求"""
    
    # 连接到上游代理
    reader, writer = await asyncio.open_connection('gw.dataimpulse.com', 19965)
    
    try:
        # 构建CONNECT请求
        request = "CONNECT httpbin.org:80 HTTP/1.1\r\n"
        
        # 添加认证头
        auth_string = "0465846b31e1f583b17c__cr.us:16af34bf75f0573a"
        auth_bytes = auth_string.encode('utf-8')
        auth_b64 = base64.b64encode(auth_bytes).decode('ascii')
        request += f"Proxy-Authorization: Basic {auth_b64}\r\n"
        request += "\r\n"
        
        print("发送CONNECT请求:")
        print(request)
        print("=" * 50)
        
        # 发送请求
        writer.write(request.encode())
        await writer.drain()
        
        # 读取CONNECT响应
        print("接收CONNECT响应:")
        response_line = await reader.readline()
        print(f"状态行: {response_line.decode('utf-8', 'ignore').strip()}")
        
        # 读取响应头
        while True:
            header_line = await reader.readline()
            if header_line in (b'\r\n', b'\n', b''):
                break
            print(f"头部: {header_line.decode('utf-8', 'ignore').strip()}")
        
        # 如果CONNECT成功，发送HTTP请求
        if b'200' in response_line:
            print("\nCONNECT成功！现在发送HTTP请求...")
            
            http_request = "GET /ip HTTP/1.1\r\n"
            http_request += "Host: httpbin.org\r\n"
            http_request += "User-Agent: Python-Debug/1.0\r\n"
            http_request += "Connection: close\r\n"
            http_request += "\r\n"
            
            print("发送HTTP请求:")
            print(http_request)
            
            writer.write(http_request.encode())
            await writer.drain()
            
            # 读取HTTP响应
            print("接收HTTP响应:")
            response_data = b''
            while True:
                try:
                    data = await asyncio.wait_for(reader.read(4096), timeout=5.0)
                    if not data:
                        break
                    response_data += data
                    print(data.decode('utf-8', 'ignore'), end='')
                except asyncio.TimeoutError:
                    print("\n[超时，停止读取]")
                    break
            
            print(f"\n总共接收到 {len(response_data)} 字节")
        else:
            print("CONNECT失败！")
        
    finally:
        writer.close()
        await writer.wait_closed()

if __name__ == "__main__":
    asyncio.run(debug_connect_request())
