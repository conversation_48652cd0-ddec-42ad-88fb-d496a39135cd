import asyncio
import logging
import time
import base64
from typing import Optional
from urllib.parse import urlparse
import socket
from threading import Thread

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class HttpProxyRelayServer:
    def __init__(self, upstream_config: dict):
        self.upstream_config = upstream_config
        self.local_host = '127.0.0.1'
        self.local_port = None
        self.server = None
        self.client_tasks = set()
        self._loop = None
        self._thread = None
        self._shutdown_event = asyncio.Event()

    def start(self, local_host: str = '127.0.0.1'):
        """Starts the proxy server in a background thread."""
        if self._thread and self._thread.is_alive():
            logger.warning("Server is already running.")
            return

        self.local_host = local_host
        self._shutdown_event.clear()
        self._thread = Thread(target=self._run_server_thread, daemon=True)
        self._thread.start()
        logger.info("HTTP Proxy server thread started.")

    def stop(self):
        """Stops the proxy server gracefully."""
        if not self._thread or not self._thread.is_alive():
            logger.warning("Server is not running.")
            return

        if self._loop and not self._shutdown_event.is_set():
            logger.info("Initiating server shutdown...")
            self._loop.call_soon_threadsafe(self._shutdown_event.set)
            self._thread.join(timeout=10)
            if self._thread.is_alive():
                logger.error("Server thread failed to stop in time.")
        logger.info("HTTP Proxy server stopped.")

    def _run_server_thread(self):
        """The main execution target for the server thread."""
        try:
            self._loop = asyncio.new_event_loop()
            asyncio.set_event_loop(self._loop)
            self._loop.run_until_complete(self._start_and_serve())
        except Exception as e:
            logger.error(f"Error in server thread: {e}", exc_info=True)
        finally:
            self._loop.close()
            logger.info("Event loop closed.")

    async def _start_and_serve(self):
        """Starts the server, serves requests, and handles shutdown."""
        self.local_port = self._find_available_port()
        
        self.server = await asyncio.start_server(
            self._accept_client, self.local_host, self.local_port
        )
        logger.info(f"HTTP Proxy server listening on {self.local_host}:{self.local_port}")

        await self._shutdown_event.wait()
        await self._cleanup()

    async def _accept_client(self, client_reader: asyncio.StreamReader,
                             client_writer: asyncio.StreamWriter):
        """Accepts a new client and creates a task to handle it."""
        try:
            task = asyncio.create_task(
                self._handle_http_request(client_reader, client_writer)
            )
            self.client_tasks.add(task)
            task.add_done_callback(self.client_tasks.discard)
        except Exception as e:
            logger.error(f"Error accepting client: {e}")
            client_writer.close()

    async def _handle_http_request(self, client_reader: asyncio.StreamReader,
                                   client_writer: asyncio.StreamWriter):
        """处理HTTP请求，转发到上游HTTP代理"""
        try:
            # 读取请求行
            request_line = await client_reader.readline()
            if not request_line:
                return

            request_line_str = request_line.decode('utf-8', 'ignore').strip()
            logger.info(f"收到请求: {request_line_str}")

            # 解析请求
            parts = request_line_str.split()
            if len(parts) < 3:
                await self._send_error_response(client_writer, 400, "Bad Request")
                return

            method, url, http_version = parts

            if method == 'CONNECT':
                # HTTPS CONNECT请求
                await self._handle_connect_request(client_reader, client_writer, url)
            else:
                # HTTP请求，转换为CONNECT隧道
                await self._handle_http_via_connect(client_reader, client_writer,
                                                  method, url, http_version, request_line)

        except Exception as e:
            logger.error(f"处理HTTP请求失败: {e}", exc_info=True)
            await self._send_error_response(client_writer, 502, "Bad Gateway")
            
        finally:
            if not client_writer.is_closing():
                client_writer.close()
                await client_writer.wait_closed()

    async def _handle_connect_request(self, client_reader, client_writer, target):
        """处理CONNECT请求"""
        try:
            # 跳过客户端的请求头
            while True:
                header_line = await client_reader.readline()
                if header_line in (b'\r\n', b'\n', b''):
                    break

            # 连接到上游代理
            upstream_reader, upstream_writer = await asyncio.open_connection(
                self.upstream_config['host'],
                self.upstream_config['port']
            )

            try:
                # 发送CONNECT请求到上游代理
                connect_request = f"CONNECT {target} HTTP/1.1\r\n"

                # 添加认证头
                if self.upstream_config.get('username') and self.upstream_config.get('password'):
                    auth_string = f"{self.upstream_config['username']}:{self.upstream_config['password']}"
                    auth_bytes = auth_string.encode('utf-8')
                    auth_b64 = base64.b64encode(auth_bytes).decode('ascii')
                    connect_request += f"Proxy-Authorization: Basic {auth_b64}\r\n"

                connect_request += "\r\n"

                upstream_writer.write(connect_request.encode())
                await upstream_writer.drain()

                # 读取上游代理的响应
                response_line = await upstream_reader.readline()
                response_str = response_line.decode('utf-8', 'ignore').strip()
                logger.info(f"上游CONNECT响应: {response_str}")

                # 跳过响应头
                while True:
                    header_line = await upstream_reader.readline()
                    if header_line in (b'\r\n', b'\n', b''):
                        break

                # 检查响应状态
                if b'200' in response_line:
                    # 发送成功响应给客户端
                    client_writer.write(b'HTTP/1.1 200 Connection Established\r\n\r\n')
                    await client_writer.drain()

                    # 开始双向数据转发
                    await self._forward_data_bidirectional(client_reader, client_writer,
                                                         upstream_reader, upstream_writer)
                else:
                    # 转发错误响应
                    client_writer.write(response_line)
                    await client_writer.drain()

            finally:
                upstream_writer.close()
                await upstream_writer.wait_closed()

        except Exception as e:
            logger.error(f"CONNECT请求处理失败: {e}", exc_info=True)
            await self._send_error_response(client_writer, 502, "Bad Gateway")

    async def _handle_http_via_connect(self, client_reader, client_writer,
                                     method, url, http_version, original_request_line):
        """通过CONNECT隧道处理HTTP请求"""
        try:
            # 读取所有客户端请求头和请求体
            headers = []
            body = b''
            content_length = 0

            while True:
                header_line = await client_reader.readline()
                if header_line in (b'\r\n', b'\n', b''):
                    break
                header_str = header_line.decode('utf-8', 'ignore').strip()
                headers.append(header_str)
                if header_str.lower().startswith('content-length:'):
                    content_length = int(header_str.split(':', 1)[1].strip())

            # 读取请求体
            if content_length > 0:
                body = await client_reader.readexactly(content_length)

            # 解析目标主机和端口
            if url.startswith('http://'):
                url_without_scheme = url[7:]
                default_port = 80
                path = '/' + '/'.join(url_without_scheme.split('/')[1:]) if '/' in url_without_scheme else '/'
            else:
                # 从Host头获取
                host_header = None
                for header in headers:
                    if header.lower().startswith('host:'):
                        host_header = header.split(':', 1)[1].strip()
                        break

                if not host_header:
                    await self._send_error_response(client_writer, 400, "Bad Request - No Host header")
                    return

                url_without_scheme = host_header
                default_port = 80
                path = url

            # 解析主机和端口
            if ':' in url_without_scheme:
                host, port_str = url_without_scheme.split(':', 1)
                port = int(port_str.split('/')[0])
            else:
                host = url_without_scheme.split('/')[0]
                port = default_port

            target = f"{host}:{port}"
            logger.info(f"HTTP请求目标: {target}, 路径: {path}")

            # 建立到上游代理的CONNECT隧道
            upstream_reader, upstream_writer = await asyncio.open_connection(
                self.upstream_config['host'],
                self.upstream_config['port']
            )

            try:
                # 发送CONNECT请求到上游代理
                connect_request = f"CONNECT {target} HTTP/1.1\r\n"

                # 添加认证头
                if self.upstream_config.get('username') and self.upstream_config.get('password'):
                    auth_string = f"{self.upstream_config['username']}:{self.upstream_config['password']}"
                    auth_bytes = auth_string.encode('utf-8')
                    auth_b64 = base64.b64encode(auth_bytes).decode('ascii')
                    connect_request += f"Proxy-Authorization: Basic {auth_b64}\r\n"

                connect_request += "\r\n"

                upstream_writer.write(connect_request.encode())
                await upstream_writer.drain()

                # 读取上游代理的响应
                response_line = await upstream_reader.readline()
                response_str = response_line.decode('utf-8', 'ignore').strip()
                logger.info(f"上游CONNECT响应: {response_str}")

                # 跳过响应头
                while True:
                    header_line = await upstream_reader.readline()
                    if header_line in (b'\r\n', b'\n', b''):
                        break

                # 检查响应状态
                if b'200' in response_line:
                    # CONNECT隧道建立成功，现在发送原始HTTP请求

                    # 重新构建HTTP请求（使用相对路径）
                    http_request = f"{method} {path} {http_version}\r\n"
                    for header in headers:
                        # 移除代理相关的头部
                        if not header.lower().startswith('proxy-'):
                            http_request += f"{header}\r\n"
                    http_request += "\r\n"

                    # 发送HTTP请求到目标服务器（通过隧道）
                    upstream_writer.write(http_request.encode())
                    if body:
                        upstream_writer.write(body)
                    await upstream_writer.drain()

                    logger.info(f"已通过隧道发送HTTP请求: {method} {path}")

                    # 开始双向数据转发
                    await self._forward_data_bidirectional(client_reader, client_writer,
                                                         upstream_reader, upstream_writer)
                else:
                    # 转发错误响应
                    client_writer.write(response_line)
                    await client_writer.drain()

            finally:
                upstream_writer.close()
                await upstream_writer.wait_closed()

        except Exception as e:
            logger.error(f"HTTP via CONNECT处理失败: {e}", exc_info=True)
            await self._send_error_response(client_writer, 502, "Bad Gateway")

    async def _forward_data_bidirectional(self, client_reader, client_writer,
                                        upstream_reader, upstream_writer):
        """双向数据转发"""
        async def copy_data(reader, writer, name):
            try:
                while True:
                    data = await reader.read(4096)
                    if not data:
                        logger.info(f"{name} 连接关闭")
                        break
                    writer.write(data)
                    await writer.drain()
            except Exception as e:
                logger.error(f"{name} 数据转发错误: {e}")
            finally:
                if not writer.is_closing():
                    writer.close()

        # 启动双向转发任务
        task1 = asyncio.create_task(copy_data(client_reader, upstream_writer, "客户端->上游"))
        task2 = asyncio.create_task(copy_data(upstream_reader, client_writer, "上游->客户端"))

        # 等待任一任务完成
        done, pending = await asyncio.wait([task1, task2], return_when=asyncio.FIRST_COMPLETED)

        # 取消剩余任务
        for task in pending:
            task.cancel()

        logger.info("双向数据转发完成")

    async def _send_error_response(self, writer, status_code, reason):
        """发送错误响应"""
        try:
            response = f"HTTP/1.1 {status_code} {reason}\r\n"
            response += "Content-Type: text/plain\r\n"
            response += f"Content-Length: {len(reason)}\r\n"
            response += "\r\n"
            response += reason
            
            writer.write(response.encode())
            await writer.drain()
        except Exception as e:
            logger.error(f"发送错误响应失败: {e}")

    async def _cleanup(self):
        """Cleans up resources during shutdown."""
        logger.info("Starting cleanup...")
        self.server.close()
        await self.server.wait_closed()
        logger.info("Server has stopped accepting new connections.")

        if self.client_tasks:
            logger.info(f"Cancelling {len(self.client_tasks)} active client tasks...")
            await asyncio.gather(*[task for task in self.client_tasks], return_exceptions=True)
        
        logger.info("Cleanup complete.")

    def _find_available_port(self) -> int:
        """Finds and returns an available TCP port."""
        with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
            s.bind((self.local_host, 0))
            return s.getsockname()[1]

    def get_proxy_url(self, protocol: str = 'http') -> Optional[str]:
        """Returns the proxy URL if the server is running."""
        if not self.local_port:
            logger.warning("Server not running, cannot get proxy URL.")
            return None
        return f"{protocol}://{self.local_host}:{self.local_port}"


class HttpRelay:
    def __init__(self, upstream_url: Optional[str] = None):
        self.upstream_config: Optional[dict] = None
        self.server: Optional[HttpProxyRelayServer] = None

        if upstream_url:
            self.set_upstream_from_url(upstream_url)

    def set_upstream_from_url(self, upstream_url: str):
        """从URL格式设置上游HTTP代理"""
        try:
            parsed = urlparse(upstream_url)

            if parsed.scheme.lower() not in ('http', 'https'):
                raise ValueError("URL scheme必须是http或https")

            host = parsed.hostname
            port = parsed.port or (443 if parsed.scheme == 'https' else 80)

            username = parsed.username
            password = parsed.password

            self.upstream_config = {
                'host': host,
                'port': port,
                'username': username,
                'password': password,
                'scheme': parsed.scheme
            }
            logger.info(f"上游HTTP代理已更新: {host}:{port}")

            if username and password:
                logger.info(f"使用认证: {username}:***")
            else:
                logger.info("使用无认证模式")

        except Exception as e:
            logger.error(f"解析上游URL失败: {e}")
            self.upstream_config = None

    def start(self, local_host: str = '127.0.0.1'):
        """同步启动代理服务器（在后台线程中运行）"""
        if self.server and self.server._thread and self.server._thread.is_alive():
            logger.warning("HTTP代理服务器已在运行")
            return

        if not self.upstream_config:
            raise ValueError("未配置上游代理。请先调用 set_upstream_from_url()")

        self.server = HttpProxyRelayServer(self.upstream_config)
        self.server.start(local_host)

        # 等待服务器启动并分配端口
        start_time = time.time()
        while not self.server.local_port:
            if time.time() - start_time > 10:  # 10秒超时
                logger.error("HTTP代理服务器启动超时")
                self.stop()
                return
            time.sleep(0.1)

        logger.info(f"HTTP代理服务器已在后台线程启动: {self.get_http_url()}")

    def stop(self):
        """同步停止代理服务器"""
        if self.server:
            self.server.stop()
            self.server = None
        else:
            logger.warning("HTTP代理服务器未运行")

    def get_http_url(self) -> Optional[str]:
        """获取本地HTTP代理URL"""
        if not self.server or not self.server.local_port:
            logger.warning("代理服务器未启动或未就绪，无法获取代理URL")
            return None
        return self.server.get_proxy_url('http')

    def __enter__(self):
        """支持with语句"""
        self.start()
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        """支持with语句"""
        self.stop()
        return False  # 不抑制异常


# 示例使用
if __name__ == "__main__":
    # 使用主人提供的HTTP代理
    upstream_proxy = "http://0465846b31e1f583b17c__cr.us:<EMAIL>:19965"
    
    with HttpRelay(upstream_proxy) as relay:
        print(f"本地HTTP代理URL: {relay.get_http_url()}")
        print("HTTP代理服务器正在运行...按Ctrl+C停止")
        
        try:
            while True:
                time.sleep(1)
        except KeyboardInterrupt:
            pass
    
    print("HTTP代理服务器已停止")
