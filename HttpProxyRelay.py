import asyncio
import logging
import time
from typing import Optional
from urllib.parse import urlparse
import aiohttp
import socket
from threading import Thread

# 配置日志
logging.basicConfig(
    level=logging.WARNING,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class HttpProxyRelayServer:
    def __init__(self, upstream_config: dict):
        self.upstream_config = upstream_config
        self.local_host = '127.0.0.1'
        self.local_port = None
        self.server = None
        self.client_tasks = set()
        self._loop = None
        self._thread = None
        self._shutdown_event = asyncio.Event()

    def start(self, local_host: str = '127.0.0.1'):
        """Starts the proxy server in a background thread."""
        if self._thread and self._thread.is_alive():
            logger.warning("Server is already running.")
            return

        self.local_host = local_host
        self._shutdown_event.clear()
        self._thread = Thread(target=self._run_server_thread, daemon=True)
        self._thread.start()
        logger.info("HTTP Proxy server thread started.")

    def stop(self):
        """Stops the proxy server gracefully."""
        if not self._thread or not self._thread.is_alive():
            logger.warning("Server is not running.")
            return

        if self._loop and not self._shutdown_event.is_set():
            logger.info("Initiating server shutdown...")
            self._loop.call_soon_threadsafe(self._shutdown_event.set)
            self._thread.join(timeout=10)
            if self._thread.is_alive():
                logger.error("Server thread failed to stop in time.")
        logger.info("HTTP Proxy server stopped.")

    def _run_server_thread(self):
        """The main execution target for the server thread."""
        try:
            self._loop = asyncio.new_event_loop()
            asyncio.set_event_loop(self._loop)
            self._loop.run_until_complete(self._start_and_serve())
        except Exception as e:
            logger.error(f"Error in server thread: {e}", exc_info=True)
        finally:
            self._loop.close()
            logger.info("Event loop closed.")

    async def _start_and_serve(self):
        """Starts the server, serves requests, and handles shutdown."""
        self.local_port = self._find_available_port()
        
        self.server = await asyncio.start_server(
            self._accept_client, self.local_host, self.local_port
        )
        logger.info(f"HTTP Proxy server listening on {self.local_host}:{self.local_port}")

        await self._shutdown_event.wait()
        await self._cleanup()

    async def _accept_client(self, client_reader: asyncio.StreamReader,
                             client_writer: asyncio.StreamWriter):
        """Accepts a new client and creates a task to handle it."""
        try:
            task = asyncio.create_task(
                self._handle_http_request(client_reader, client_writer)
            )
            self.client_tasks.add(task)
            task.add_done_callback(self.client_tasks.discard)
        except Exception as e:
            logger.error(f"Error accepting client: {e}")
            client_writer.close()

    async def _handle_http_request(self, client_reader: asyncio.StreamReader,
                                   client_writer: asyncio.StreamWriter):
        """处理HTTP请求，转发到上游HTTP代理"""
        try:
            # 读取HTTP请求行
            request_line = await client_reader.readline()
            if not request_line:
                return
            
            request_line_str = request_line.decode('utf-8', 'ignore').strip()
            logger.info(f"收到请求: {request_line_str}")
            
            # 解析请求方法和URL
            parts = request_line_str.split()
            if len(parts) < 3:
                await self._send_error_response(client_writer, 400, "Bad Request")
                return
            
            method, url, http_version = parts
            
            # 读取所有请求头
            headers = {}
            while True:
                header_line = await client_reader.readline()
                if header_line in (b'\r\n', b'\n', b''):
                    break
                header_str = header_line.decode('utf-8', 'ignore').strip()
                if ':' in header_str:
                    key, value = header_str.split(':', 1)
                    headers[key.strip()] = value.strip()
            
            # 读取请求体（如果有Content-Length）
            body = b''
            if 'Content-Length' in headers:
                content_length = int(headers['Content-Length'])
                if content_length > 0:
                    body = await client_reader.readexactly(content_length)
            
            # 通过上游HTTP代理转发请求
            await self._forward_to_upstream(client_writer, method, url, headers, body)
            
        except asyncio.CancelledError:
            logger.info("Client handling task was cancelled.")
        except Exception as e:
            peername = client_writer.get_extra_info('peername')
            logger.error(f"Error handling HTTP request from {peername}: {e}", exc_info=True)
            await self._send_error_response(client_writer, 502, "Bad Gateway")
        finally:
            if not client_writer.is_closing():
                client_writer.close()
                await client_writer.wait_closed()

    async def _forward_to_upstream(self, client_writer, method, url, headers, body):
        """通过上游HTTP代理转发请求"""
        try:
            # 构建代理URL
            proxy_url = f"http://{self.upstream_config['username']}:{self.upstream_config['password']}@{self.upstream_config['host']}:{self.upstream_config['port']}"
            
            # 创建aiohttp会话，使用上游代理
            connector = aiohttp.TCPConnector()
            timeout = aiohttp.ClientTimeout(total=30)
            
            async with aiohttp.ClientSession(
                connector=connector,
                timeout=timeout
            ) as session:
                # 发送请求到上游代理
                async with session.request(
                    method=method,
                    url=url,
                    headers=headers,
                    data=body,
                    proxy=proxy_url,
                    allow_redirects=False
                ) as upstream_response:
                    # 转发响应状态行
                    status_line = f"HTTP/1.1 {upstream_response.status} {upstream_response.reason}\r\n"
                    client_writer.write(status_line.encode())
                    
                    # 转发响应头
                    for header_name, header_value in upstream_response.headers.items():
                        if header_name.lower() not in ['connection', 'transfer-encoding']:
                            header_line = f"{header_name}: {header_value}\r\n"
                            client_writer.write(header_line.encode())
                    
                    # 结束头部
                    client_writer.write(b'\r\n')
                    await client_writer.drain()
                    
                    # 转发响应体
                    async for chunk in upstream_response.content.iter_chunked(4096):
                        client_writer.write(chunk)
                        await client_writer.drain()
                    
                    logger.info(f"成功转发 {method} {url} -> {upstream_response.status}")
                    
        except Exception as e:
            logger.error(f"转发到上游代理失败: {e}", exc_info=True)
            await self._send_error_response(client_writer, 502, "Bad Gateway")

    async def _send_error_response(self, writer, status_code, reason):
        """发送错误响应"""
        try:
            response = f"HTTP/1.1 {status_code} {reason}\r\n"
            response += "Content-Type: text/plain\r\n"
            response += f"Content-Length: {len(reason)}\r\n"
            response += "\r\n"
            response += reason
            
            writer.write(response.encode())
            await writer.drain()
        except Exception as e:
            logger.error(f"发送错误响应失败: {e}")

    async def _cleanup(self):
        """Cleans up resources during shutdown."""
        logger.info("Starting cleanup...")
        self.server.close()
        await self.server.wait_closed()
        logger.info("Server has stopped accepting new connections.")

        if self.client_tasks:
            logger.info(f"Cancelling {len(self.client_tasks)} active client tasks...")
            await asyncio.gather(*[task for task in self.client_tasks], return_exceptions=True)
        
        logger.info("Cleanup complete.")

    def _find_available_port(self) -> int:
        """Finds and returns an available TCP port."""
        with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
            s.bind((self.local_host, 0))
            return s.getsockname()[1]

    def get_proxy_url(self, protocol: str = 'http') -> Optional[str]:
        """Returns the proxy URL if the server is running."""
        if not self.local_port:
            logger.warning("Server not running, cannot get proxy URL.")
            return None
        return f"{protocol}://{self.local_host}:{self.local_port}"


class HttpRelay:
    def __init__(self, upstream_url: Optional[str] = None):
        self.upstream_config: Optional[dict] = None
        self.server: Optional[HttpProxyRelayServer] = None

        if upstream_url:
            self.set_upstream_from_url(upstream_url)

    def set_upstream_from_url(self, upstream_url: str):
        """从URL格式设置上游HTTP代理"""
        try:
            parsed = urlparse(upstream_url)

            if parsed.scheme.lower() not in ('http', 'https'):
                raise ValueError("URL scheme必须是http或https")

            host = parsed.hostname
            port = parsed.port or (443 if parsed.scheme == 'https' else 80)

            username = parsed.username
            password = parsed.password

            self.upstream_config = {
                'host': host,
                'port': port,
                'username': username,
                'password': password,
                'scheme': parsed.scheme
            }
            logger.info(f"上游HTTP代理已更新: {host}:{port}")

            if username and password:
                logger.info(f"使用认证: {username}:***")
            else:
                logger.info("使用无认证模式")

        except Exception as e:
            logger.error(f"解析上游URL失败: {e}")
            self.upstream_config = None

    def start(self, local_host: str = '127.0.0.1'):
        """同步启动代理服务器（在后台线程中运行）"""
        if self.server and self.server._thread and self.server._thread.is_alive():
            logger.warning("HTTP代理服务器已在运行")
            return

        if not self.upstream_config:
            raise ValueError("未配置上游代理。请先调用 set_upstream_from_url()")

        self.server = HttpProxyRelayServer(self.upstream_config)
        self.server.start(local_host)

        # 等待服务器启动并分配端口
        start_time = time.time()
        while not self.server.local_port:
            if time.time() - start_time > 10:  # 10秒超时
                logger.error("HTTP代理服务器启动超时")
                self.stop()
                return
            time.sleep(0.1)

        logger.info(f"HTTP代理服务器已在后台线程启动: {self.get_http_url()}")

    def stop(self):
        """同步停止代理服务器"""
        if self.server:
            self.server.stop()
            self.server = None
        else:
            logger.warning("HTTP代理服务器未运行")

    def get_http_url(self) -> Optional[str]:
        """获取本地HTTP代理URL"""
        if not self.server or not self.server.local_port:
            logger.warning("代理服务器未启动或未就绪，无法获取代理URL")
            return None
        return self.server.get_proxy_url('http')

    def __enter__(self):
        """支持with语句"""
        self.start()
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        """支持with语句"""
        self.stop()
        return False  # 不抑制异常


# 示例使用
if __name__ == "__main__":
    # 使用主人提供的HTTP代理
    upstream_proxy = "http://0465846b31e1f583b17c__cr.us:<EMAIL>:19965"
    
    with HttpRelay(upstream_proxy) as relay:
        print(f"本地HTTP代理URL: {relay.get_http_url()}")
        print("HTTP代理服务器正在运行...按Ctrl+C停止")
        
        try:
            while True:
                time.sleep(1)
        except KeyboardInterrupt:
            pass
    
    print("HTTP代理服务器已停止")
