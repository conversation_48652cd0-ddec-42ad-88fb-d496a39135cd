# Bypasser Demo 代理使用指南

本文档说明如何在 `bypasser_demo.py` 中使用代理功能。

## 功能概述

`bypasser_demo.py` 现在支持通过代理访问网站，特别适用于需要通过代理绕过 Cloudflare 验证的场景。

## 使用方法

### 1. 不使用代理（默认）

```python
from bypasser_demo import demo_cloudflare_bypass

# 直接访问，不使用代理
demo_cloudflare_bypass('https://nopecha.com/demo/cloudflare')
```

### 2. 使用代理（手动管理）

```python
from bypasser_demo import demo_cloudflare_bypass

# 使用代理访问
UPSTREAM_PROXY = "socks5://user:<EMAIL>:1080"
demo_cloudflare_bypass('https://nopecha.com/demo/cloudflare', UPSTREAM_PROXY)
```

### 3. 使用代理（推荐方式，自动管理）

```python
from bypasser_demo import demo_cloudflare_bypass_with_proxy

# 使用 with 语句自动管理代理生命周期
UPSTREAM_PROXY = "socks5://user:<EMAIL>:1080"
demo_cloudflare_bypass_with_proxy('https://nopecha.com/demo/cloudflare', UPSTREAM_PROXY)
```

## 代理格式

支持以下 SOCKS5 代理格式：

- **带认证**: `socks5://username:<EMAIL>:1080`
- **无认证**: `socks5://proxy.com:1080`
- **本地代理**: `socks5://127.0.0.1:9000`
- **SOCKS5h**: `socks5h://username:<EMAIL>:1080`

## 工作原理

1. **代理中继**: 使用 `ProxyRelay.Socks5Relay` 类在本地创建一个代理中继服务
2. **协议转换**: 将上游 SOCKS5 代理转换为本地 HTTP 代理
3. **浏览器配置**: 配置 ChromiumPage 使用本地 HTTP 代理
4. **自动管理**: 自动处理代理的启动和关闭

## 示例代码

### 完整示例

```python
from bypasser_demo import demo_cloudflare_bypass_with_proxy

# 配置你的代理
UPSTREAM_PROXY = "socks5://your-username:<EMAIL>:1080"

# 测试网站列表
test_urls = [
    'https://nopecha.com/demo/cloudflare',
    'https://www.potatochipssettlement.com/Claim',
    'https://httpbin.org/ip'  # 查看IP地址
]

# 逐个测试
for url in test_urls:
    print(f"\n测试网站: {url}")
    try:
        demo_cloudflare_bypass_with_proxy(url, UPSTREAM_PROXY)
    except Exception as e:
        print(f"测试失败: {e}")
```

### 批量测试不同代理

```python
from bypasser_demo import demo_cloudflare_bypass

# 多个代理配置
proxies = [
    "socks5://proxy1.com:1080",
    "socks5://user:<EMAIL>:1080",
    "socks5://127.0.0.1:9000"
]

url = 'https://nopecha.com/demo/cloudflare'

for proxy in proxies:
    print(f"\n使用代理: {proxy}")
    try:
        demo_cloudflare_bypass(url, proxy)
    except Exception as e:
        print(f"代理失败: {e}")
```

## 注意事项

1. **代理有效性**: 确保上游代理地址正确且可访问
2. **认证信息**: 如果代理需要认证，请提供正确的用户名和密码
3. **网络延迟**: 使用代理可能会增加网络延迟
4. **资源清理**: 推荐使用 `demo_cloudflare_bypass_with_proxy` 函数，它会自动管理代理资源

## 故障排除

### 常见问题

1. **代理启动失败**
   - 检查代理地址格式是否正确
   - 确认代理服务器是否可访问
   - 验证认证信息是否正确

2. **浏览器启动失败**
   - 检查是否有其他浏览器实例占用端口
   - 确认系统资源是否充足

3. **页面加载失败**
   - 检查目标网站是否可访问
   - 确认代理是否支持目标网站的地理位置

### 调试模式

可以通过修改日志级别来获取更多调试信息：

```python
import logging
logging.basicConfig(level=logging.INFO)

# 然后运行你的代码
demo_cloudflare_bypass_with_proxy(url, proxy)
```

## 性能优化

1. **复用代理连接**: 对于多个请求，考虑复用同一个代理实例
2. **合理设置超时**: 根据网络情况调整超时时间
3. **选择合适的代理**: 选择地理位置和网络质量较好的代理服务器
