#!/usr/bin/env python3
"""
HTTP代理中转Demo
参考DrissionPage-Proxy架构，实现HTTP代理到本地的中转
"""

import time
import requests
from HttpProxyRelay import HttpRelay


def test_http_proxy():
    """测试HTTP代理中转功能"""
    # 主人的HTTP代理
    upstream_proxy = "http://0465846b31e1f583b17c__cr.us:<EMAIL>:19965"
    
    print("🚀 启动HTTP代理中转服务...")
    
    with HttpRelay(upstream_proxy) as relay:
        local_proxy_url = relay.get_http_url()
        print(f"✅ 本地HTTP代理已启动: {local_proxy_url}")
        
        # 测试代理功能
        print("\n🔍 测试代理功能...")
        
        try:
            # 使用本地代理访问测试网站
            proxies = {
                'http': local_proxy_url,
                'https': local_proxy_url
            }
            
            # 测试1: 获取IP地址
            print("📡 测试1: 获取当前IP地址...")
            response = requests.get('http://httpbin.org/ip', proxies=proxies, timeout=10)
            if response.status_code == 200:
                ip_info = response.json()
                print(f"✅ 当前IP: {ip_info.get('origin', 'Unknown')}")
            else:
                print(f"❌ 获取IP失败: {response.status_code}")
            
            # 测试2: 获取请求头信息
            print("\n📡 测试2: 获取请求头信息...")
            response = requests.get('http://httpbin.org/headers', proxies=proxies, timeout=10)
            if response.status_code == 200:
                headers_info = response.json()
                print("✅ 请求头信息获取成功")
                print(f"   User-Agent: {headers_info.get('headers', {}).get('User-Agent', 'Unknown')}")
            else:
                print(f"❌ 获取请求头失败: {response.status_code}")
            
            # 测试3: POST请求
            print("\n📡 测试3: POST请求...")
            test_data = {'message': 'Hello from HTTP Proxy Relay!', 'timestamp': str(time.time())}
            response = requests.post('http://httpbin.org/post', json=test_data, proxies=proxies, timeout=10)
            if response.status_code == 200:
                post_result = response.json()
                print("✅ POST请求成功")
                print(f"   发送数据: {post_result.get('json', {})}")
            else:
                print(f"❌ POST请求失败: {response.status_code}")
            
            # 测试4: HTTPS请求
            print("\n📡 测试4: HTTPS请求...")
            response = requests.get('https://httpbin.org/ip', proxies=proxies, timeout=10, verify=False)
            if response.status_code == 200:
                https_ip = response.json()
                print(f"✅ HTTPS请求成功，IP: {https_ip.get('origin', 'Unknown')}")
            else:
                print(f"❌ HTTPS请求失败: {response.status_code}")
                
        except requests.exceptions.RequestException as e:
            print(f"❌ 请求异常: {e}")
        except Exception as e:
            print(f"❌ 测试过程中发生错误: {e}")
        
        print(f"\n🎉 测试完成！代理服务器运行在: {local_proxy_url}")
        print("💡 你可以在其他应用中使用这个本地代理URL")
        
        # 保持服务运行一段时间供手动测试
        print("\n⏰ 代理服务器将继续运行60秒，供手动测试...")
        print("   可以在浏览器或其他工具中配置代理: " + local_proxy_url)
        print("   按Ctrl+C可提前停止")
        
        try:
            for i in range(60, 0, -1):
                print(f"\r   剩余时间: {i:2d}秒", end='', flush=True)
                time.sleep(1)
        except KeyboardInterrupt:
            print("\n\n⏹️  用户手动停止")
        
        print("\n\n🛑 代理服务器即将停止...")
    
    print("✅ HTTP代理中转服务已停止")


def test_with_drissionpage():
    """演示如何与DrissionPage集成使用"""
    print("\n" + "="*50)
    print("📚 DrissionPage集成示例")
    print("="*50)
    
    upstream_proxy = "http://0465846b31e1f583b17c__cr.us:<EMAIL>:19965"
    
    print("💡 使用方法:")
    print(f"""
from HttpProxyRelay import HttpRelay
from DrissionPage import ChromiumPage, ChromiumOptions

# 启动HTTP代理中转
with HttpRelay("{upstream_proxy}") as relay:
    local_proxy = relay.get_http_url()
    print(f"本地代理: {{local_proxy}}")
    
    # 配置DrissionPage使用本地代理
    options = ChromiumOptions().auto_port()
    options.set_proxy(local_proxy)
    
    # 创建页面实例
    page = ChromiumPage(addr_or_opts=options)
    
    # 访问网站
    page.get('https://httpbin.org/ip')
    print("当前IP:", page.html)
    
    # 其他操作...
    page.quit()
""")


if __name__ == "__main__":
    print("🎯 HTTP代理中转Demo")
    print("参考DrissionPage-Proxy架构实现")
    print("="*50)
    
    # 运行基本测试
    test_http_proxy()
    
    # 显示DrissionPage集成示例
    test_with_drissionpage()
    
    print("\n🎉 Demo演示完成！")
