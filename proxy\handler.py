import asyncio
import logging
import socket
from typing import Tuple

logger = logging.getLogger(__name__)


class Socks5UpstreamHandler:
    def __init__(self, upstream_config: dict):
        self.upstream_config = upstream_config

    async def handle_request(self, client_reader: asyncio.StreamReader,
                             client_writer: asyncio.StreamWriter,
                             initial_data: bytes):
        """
        Handles a client request by sniffing the protocol and dispatching
        to the appropriate handler.
        """
        if not self.upstream_config:
            logger.error("Upstream proxy not configured.")
            client_writer.close()
            return

        try:
            if initial_data and initial_data[0] == 0x05:  # SOCKS5
                await self._handle_socks_client(client_reader, client_writer, initial_data)
            else:  # HTTP
                await self._handle_http_request(client_reader, client_writer, initial_data)
        except asyncio.CancelledError:
            logger.info("Client handling task was cancelled.")
        except Exception as e:
            peername = client_writer.get_extra_info('peername')
            logger.error(f"Error handling client {peername}: {e}", exc_info=True)
        finally:
            if not client_writer.is_closing():
                client_writer.close()
                await client_writer.wait_closed()

    async def _handle_http_request(self, client_reader: asyncio.StreamReader,
                                   client_writer: asyncio.StreamWriter,
                                   initial_data: bytes):
        """分发HTTP CONNECT和其他HTTP方法。"""
        # 重构请求行
        request_line_bytes = initial_data + await client_reader.readline()
        request_line = request_line_bytes.decode('utf-8', 'ignore').strip()
        parts = request_line.split()

        if len(parts) < 3:
            logger.warning(f"格式错误的HTTP请求行: {request_line!r}")
            client_writer.write(b'HTTP/1.1 400 Bad Request\r\n\r\n')
            await client_writer.drain()
            return

        method = parts[0].upper()
        if method == 'CONNECT':
            await self._handle_http_connect(client_reader, client_writer, parts)
        else:
            await self._handle_http_forward(client_reader, client_writer, request_line_bytes, parts)

    async def _handle_http_connect(self, client_reader: asyncio.StreamReader,
                                     client_writer: asyncio.StreamWriter,
                                     request_parts: list):
        """处理HTTP CONNECT请求（隧道）。"""
        try:
            # 从请求中解析目标主机和端口
            target_host, target_port_str = request_parts[1].split(':', 1)
            target_port = int(target_port_str)

            # 读取并忽略所有剩余的请求头
            while (await client_reader.readline()) not in (b'\r\n', b''):
                pass

        except Exception as e:
            logger.error(f"解析HTTP CONNECT请求失败: {e}")
            if not client_writer.is_closing():
                client_writer.write(b'HTTP/1.1 400 Bad Request\r\n\r\n')
                await client_writer.drain()
            return

        # 对于CONNECT请求，我们只建立隧道
        await self._connect_and_forward(
            client_reader, client_writer, target_host, target_port, is_http_connect=True
        )

    async def _handle_http_forward(self, client_reader: asyncio.StreamReader,
                                   client_writer: asyncio.StreamWriter,
                                   request_line_bytes: bytes,
                                   request_parts: list):
        """处理标准的HTTP转发请求 (GET, POST, etc.)。"""
        upstream_reader = None
        upstream_writer = None
        try:
            from urllib.parse import urlparse

            method, url_str, http_version = request_parts
            parsed_url = urlparse(url_str)
            target_host = parsed_url.hostname
            target_port = parsed_url.port or (443 if parsed_url.scheme == 'https' else 80)

            if not target_host:
                raise ValueError("无法从请求中确定目标主机")

            # 连接到上游并最终连接到目标
            upstream_reader, upstream_writer = await self._establish_upstream_connection(target_host, target_port)
            
            # 向上游发送SOCKS响应确认
            response = await upstream_reader.readexactly(10)
            if response[1] != 0x00:
                raise ConnectionRefusedError(f"上游连接失败，状态码 {response[1]}")
            logger.info(f"HTTP {method} to {target_host}:{target_port} -> 成功，准备转发请求")

            # 转发完整的客户端请求（包括我们已经读取的行）
            upstream_writer.write(request_line_bytes)
            
            # 双向转发流量
            await self._forward_traffic(client_reader, client_writer,
                                        upstream_reader, upstream_writer)

        except Exception as e:
            logger.error(f"转发HTTP请求失败: {e}", exc_info=True)
            if not client_writer.is_closing():
                try:
                    client_writer.write(b'HTTP/1.1 502 Bad Gateway\r\n\r\n')
                    await client_writer.drain()
                except Exception as close_e:
                    logger.warning(f"关闭错误连接时出错: {close_e}")
        finally:
            if upstream_writer and not upstream_writer.is_closing():
                upstream_writer.close()
                await upstream_writer.wait_closed()

    async def _handle_socks_client(self, client_reader: asyncio.StreamReader,
                                   client_writer: asyncio.StreamWriter,
                                   initial_data: bytes):
        """Handles SOCKS5 client requests."""
        try:
            # SOCKS5 handshake
            if initial_data[0] != 0x05:
                raise ValueError("Not a SOCKS5 protocol request.")

            nmethods = (await client_reader.readexactly(1))[0]
            await client_reader.readexactly(nmethods)  # Read supported methods

            # Respond with "No Authentication Required"
            client_writer.write(b'\x05\x00')
            await client_writer.drain()

            # Get target address from client
            target_addr, target_port = await self._get_socks_target_address(client_reader)

        except Exception as e:
            logger.error(f"SOCKS5 handshake failed: {e}")
            return

        await self._connect_and_forward(
            client_reader, client_writer, target_addr, target_port, is_http_connect=False
        )

    async def _connect_and_forward(self, client_reader, client_writer,
                                   target_addr, target_port, is_http_connect=False):
        """连接到上游代理并转发流量。仅用于SOCKS和HTTP CONNECT。"""
        upstream_reader = None
        upstream_writer = None
        try:
            # 连接到上游SOCKS5服务器
            upstream_reader, upstream_writer = await self._establish_upstream_connection(target_addr, target_port)

            # 根据原始协议发送成功响应给客户端
            if is_http_connect:
                # 确认上游SOCKS连接成功
                response = await upstream_reader.readexactly(10)
                if response[1] != 0x00:
                    raise ConnectionRefusedError(f"上游连接失败，状态码 {response[1]}")
                
                logger.info(f"HTTP CONNECT to {target_addr}:{target_port} -> 成功，开始转发")
                client_writer.write(b'HTTP/1.1 200 Connection Established\r\n\r\n')
                await client_writer.drain()
                 # 转发流量
                await self._forward_traffic(client_reader, client_writer, upstream_reader, upstream_writer)
            else: # SOCKS
                # 对于SOCKS，上游响应需要在转发前发回给客户端
                response = await upstream_reader.readexactly(10)
                if response[1] != 0x00:
                    raise ConnectionRefusedError(f"上游连接失败，状态码 {response[1]}")
                
                logger.info(f"SOCKS5 CONNECT to {target_addr}:{target_port} -> 成功，开始转发")
                client_writer.write(response)
                await client_writer.drain()
                 # 转发流量
                await self._forward_traffic(client_reader, client_writer, upstream_reader, upstream_writer)

        except Exception as e:
            peername = client_writer.get_extra_info('peername')
            logger.error(f"从 {peername} 转发到 {target_addr}:{target_port} 时出错: {e}")
            if is_http_connect and not client_writer.is_closing():
                client_writer.write(b'HTTP/1.1 502 Bad Gateway\r\n\r\n')
                await client_writer.drain()
        finally:
            if upstream_writer and not upstream_writer.is_closing():
                upstream_writer.close()
                await upstream_writer.wait_closed()

    async def _establish_upstream_connection(self, target_addr, target_port) -> Tuple[asyncio.StreamReader, asyncio.StreamWriter]:
        """与上游SOCKS服务器握手并发送连接到目标的请求。"""
        # 连接到上游SOCKS5服务器
        upstream_reader, upstream_writer = await asyncio.open_connection(
            self.upstream_config['host'], self.upstream_config['port']
        )

        # 与上游执行SOCKS5握手
        await self._upstream_socks_handshake(upstream_reader, upstream_writer)

        # 向上游服务器发送连接请求
        await self._send_upstream_connect_request(upstream_writer, target_addr, target_port)

        return upstream_reader, upstream_writer

    async def _upstream_socks_handshake(self, reader: asyncio.StreamReader,
                                        writer: asyncio.StreamWriter):
        """Performs SOCKS5 handshake with the upstream server."""
        auth_methods = [0x00]  # No auth
        if self.upstream_config.get('username') and self.upstream_config.get('password'):
            auth_methods.append(0x02)  # Username/Password

        writer.write(b'\x05' + bytes([len(auth_methods)]) + bytes(auth_methods))
        await writer.drain()

        ver, chosen_method = await reader.readexactly(2)
        if ver != 0x05:
            raise ValueError("Upstream is not a SOCKS5 server.")
        if chosen_method == 0xFF:
            raise ConnectionRefusedError("Upstream requires an unsupported authentication method.")
        
        if chosen_method == 0x02:
            await self._upstream_socks_auth(reader, writer)

    async def _upstream_socks_auth(self, reader: asyncio.StreamReader,
                                   writer: asyncio.StreamWriter):
        """Performs SOCKS5 username/password authentication with upstream."""
        username = self.upstream_config['username'].encode('utf-8')
        password = self.upstream_config['password'].encode('utf-8')
        
        writer.write(b'\x01' + bytes([len(username)]) + username +
                     bytes([len(password)]) + password)
        await writer.drain()

        ver, status = await reader.readexactly(2)
        if ver != 0x01 or status != 0x00:
            raise ConnectionRefusedError("Upstream authentication failed.")

    async def _get_socks_target_address(self, reader: asyncio.StreamReader) -> Tuple[str, int]:
        """Reads the target address from a SOCKS client request."""
        ver, cmd, _, addr_type = await reader.readexactly(4)
        if ver != 0x05 or cmd != 0x01:  # CONNECT only
            raise ValueError("Unsupported SOCKS command.")

        if addr_type == 0x01:  # IPv4
            addr = socket.inet_ntoa(await reader.readexactly(4))
        elif addr_type == 0x03:  # Domain
            domain_len = (await reader.readexactly(1))[0]
            addr = (await reader.readexactly(domain_len)).decode('ascii')
        else:
            raise ValueError("Unsupported address type.")

        port = int.from_bytes(await reader.readexactly(2), 'big')
        return addr, port

    async def _send_upstream_connect_request(self, writer: asyncio.StreamWriter,
                                             addr: str, port: int):
        """Sends a SOCKS5 CONNECT request to the upstream server."""
        req = bytearray(b'\x05\x01\x00')  # VER, CMD, RSV
        try:
            # Try encoding as IPv4
            ip_bytes = socket.inet_pton(socket.AF_INET, addr)
            req.append(0x01)  # ATYP: IPv4
            req.extend(ip_bytes)
        except socket.error:
            # Assume domain name
            req.append(0x03)  # ATYP: Domain
            req.append(len(addr))
            req.extend(addr.encode('ascii'))

        req.extend(port.to_bytes(2, 'big'))
        writer.write(req)
        await writer.drain()

    async def _forward_traffic(self, r1: asyncio.StreamReader, w1: asyncio.StreamWriter,
                               r2: asyncio.StreamReader, w2: asyncio.StreamWriter):
        """Forwards data between two streams."""
        async def copy_data(reader, writer):
            try:
                while True:
                    data = await reader.read(4096)
                    if not data:
                        break
                    writer.write(data)
                    await writer.drain()
            except asyncio.CancelledError:
                pass
            except Exception:
                pass
            finally:
                if not writer.is_closing():
                    writer.close()

        task1 = asyncio.create_task(copy_data(r1, w2))
        task2 = asyncio.create_task(copy_data(r2, w1))
        
        await asyncio.gather(task1, task2, return_exceptions=True) 