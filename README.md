# DrissionPage-Proxy

一个多功能的本地代理中继工具。它可以在本地创建一个代理服务器，该服务器能接收 **SOCKS5** 和 **HTTP CONNECT** 请求，并将流量转发到指定的上游 **SOCKS5** 代理。

这个工具的主要目的是为那些原生不支持 SOCKS5 代理的客户端提供一个 HTTP 代理接口，或者在不同代理协议之间进行桥接。

## 主要功能

- **协议转换**: 在本地同时监听 SOCKS5 和 HTTP 代理请求，并将它们统一通过上游 SOCKS5 代理转发。
- **上游 SOCKS5**: 支持连接到带或不带用户名/密码认证的上游 SOCKS5 代理。
- **动态配置**: 可以在程序运行时动态切换上游代理，无需重启服务。
- **自动端口**: 自动查找本地的可用端口来启动代理服务，避免端口冲突。
- **使用便捷**: 实现上下文管理器协议，可通过 `with` 语句轻松使用，确保服务被安全关闭。

---

## 如何使用

### 安装

将 `main.py` 文件复制到你的项目中，然后导入 `Socks5Relay` 类。

```python
from main import Socks5Relay
```

### 示例 1: 使用 `with` 语句（推荐）

`with` 语句能自动管理代理服务的启动和停止，是首选的使用方式。

```python
from main import Socks5Relay
from DrissionPage import ChromiumPage, ChromiumOptions
import time

# 设置你的上游SOCKS5代理地址
# UPSTREAM_PROXY = "socks5://user:<EMAIL>:1080"  # 带认证
UPSTREAM_PROXY = "socks5://127.0.0.1:9000"                   # 无认证示例

print("正在启动代理服务...")
with Socks5Relay(UPSTREAM_PROXY) as relay:
    # 代理服务已在后台启动
    
    # 获取本地代理地址
    socks5_proxy = relay.get_socks5_url()
    http_proxy = relay.get_http_url()
    
    print(f"本地 SOCKS5 代理已就绪: {socks5_proxy}")
    print(f"本地 HTTP 代理已就绪: {http_proxy}")
    
    # --- 使用 DrissionPage 通过 HTTP 代理访问 ---
    print("\n正在使用 DrissionPage 通过中继代理访问网页...")
    options = ChromiumOptions().auto_port()
    # 即使 DrissionPage 支持 SOCKS5，这里也演示通过 HTTP 代理连接
    options.set_proxy(http_proxy) 
    
    page = ChromiumPage(addr_or_opts=options)
    page.get('https://httpbin.org/ip') # 访问一个可以看IP的网站
    print("网页内容:", page.html)
    
    print("\n代理服务运行中，按 Ctrl+C 结束...")
    try:
        while True:
            time.sleep(1)
    except KeyboardInterrupt:
        pass

print("\n程序结束，代理服务已自动停止。")

```

### 示例 2: 手动控制

如果需要更灵活地控制服务的生命周期，可以手动调用 `start()` 和 `stop()` 方法。

```python
from main import Socks5Relay
import time

# 1. 创建实例
relay = Socks5Relay()

# 2. 设置上游代理并启动
print("设置上游代理并启动...")
relay.set_upstream_from_url("socks5://user:<EMAIL>:1080")
relay.start()

# 3. 检查并使用代理
if relay.get_socks5_url():
    print(f"本地 SOCKS5 代理: {relay.get_socks5_url()}")
    print(f"本地 HTTP 代理: {relay.get_http_url()}")

    # ... 在这里使用代理 ...
    print("\n代理运行中 (10秒)...")
    time.sleep(10)

# 4. 动态更换上游代理
print("\n正在更换上游代理...")
relay.set_upstream_from_url("socks5://new-proxy.com:1080") # 更换为无认证的新代理
print("上游代理已更换，本地代理地址不变。")

# ... 继续使用新代理 ...
print("使用新代理运行中 (10秒)...")
time.sleep(10)

# 5. 停止服务
print("\n正在停止代理服务...")
relay.stop()
print("服务已停止。")
```
