# Cookie 验证功能指南

## 概述

`bypasser_demo.py` 现在包含了完整的 Cookie 验证功能，可以在获取 Cloudflare cookies 后自动验证其有效性。

## 新增功能

### 1. 自动 Cookie 验证

在成功绕过 Cloudflare 验证后，脚本会自动：
- 收集所有 cookies（包括 `cf_clearance`）
- 获取当前页面的 User-Agent
- 使用 `curl_cffi` 发送验证请求
- 分析响应内容判断验证是否成功

### 2. 智能验证判断

验证逻辑包含多重检查：
- **HTTP 状态码检查**: 确保请求成功
- **成功标记检测**: 查找页面中的成功标记
- **验证页面检测**: 检查是否仍在 Cloudflare 验证页面
- **页面标题分析**: 分析页面标题判断验证状态

### 3. curl_cffi 集成

- **优先使用 curl_cffi**: 提供更好的浏览器模拟
- **降级处理**: 如果 curl_cffi 不可用，自动使用标准 requests
- **浏览器指纹模拟**: 使用 `impersonate="chrome"` 参数

## 使用示例

### 基本使用

```python
from bypasser_demo import demo_cloudflare_bypass

# 自动验证功能已集成，无需额外配置
demo_cloudflare_bypass('https://nopecha.com/demo/cloudflare')
```

### 使用代理

```python
from bypasser_demo import demo_cloudflare_bypass_with_proxy

proxy = "socks5://user:<EMAIL>:1080"
demo_cloudflare_bypass_with_proxy('https://nopecha.com/demo/cloudflare', proxy)
```

### 手动验证 Cookies

```python
from bypasser_demo import verify_cookies_with_request

# 准备数据
url = "https://target-site.com"
cookies = {
    'cf_clearance': 'your_clearance_value',
    '__cf_bm': 'your_bm_value'
}
headers = {
    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
}
proxy = "http://127.0.0.1:8080"  # 可选

# 验证
result = verify_cookies_with_request(url, cookies, headers, proxy)
print(f"验证结果: {'成功' if result else '失败'}")
```

## 输出示例

### 成功验证

```
验证 cookies 有效性...
目标URL: https://example.com
验证详情:
  状态码: 200
  响应长度: 15234 字符
  页面标题: Welcome to Example
  包含成功标记: False
  内容包含验证标记: False
  标题包含验证标记: False
✅ Cookie 验证成功！（页面正常加载）
```

### 验证失败

```
验证 cookies 有效性...
目标URL: https://example.com
验证详情:
  状态码: 403
  响应长度: 4847 字符
  页面标题: Just a moment...
  包含成功标记: False
  内容包含验证标记: True
  标题包含验证标记: True
❌ Cookie 验证失败（仍在验证页面）
```

## 配置选项

### 安装依赖

```bash
pip install curl_cffi>=0.5.0
```

### 验证标记自定义

可以修改 `verify_cookies_with_request` 函数中的标记列表：

```python
# 成功标记
success_markers = [
    "Captcha is passed successfully!",
    "验证成功",
    "Access granted",
    "Welcome"
]

# 验证页面标记
challenge_markers = [
    "just a moment",
    "checking your browser",
    "please wait",
    "verifying you are human"
]
```

## 故障排除

### 常见问题

1. **curl_cffi 导入失败**
   ```
   警告: curl_cffi 未安装，将使用标准 requests 库
   ```
   **解决**: `pip install curl_cffi`

2. **验证总是失败**
   - 检查 cookies 是否完整
   - 确认 User-Agent 是否匹配
   - 验证代理设置是否正确

3. **请求超时**
   - 检查网络连接
   - 调整超时时间（默认30秒）
   - 确认代理服务器可用

### 调试模式

启用详细日志：

```python
import logging
logging.basicConfig(level=logging.DEBUG)

# 运行验证
verify_cookies_with_request(url, cookies)
```

## 技术细节

### 验证流程

1. **准备请求**: 组装 cookies、headers 和代理配置
2. **发送请求**: 使用 curl_cffi 或 requests 发送 GET 请求
3. **分析响应**: 检查状态码、内容和标题
4. **判断结果**: 基于多重条件判断验证是否成功

### 浏览器模拟

- **TLS 指纹**: curl_cffi 提供真实的浏览器 TLS 指纹
- **HTTP/2**: 支持 HTTP/2 协议
- **请求头**: 自动添加常见的浏览器请求头

### 代理支持

- **HTTP 代理**: 支持 HTTP/HTTPS 代理
- **SOCKS5 代理**: 通过 ProxyRelay 转换支持
- **认证**: 支持代理用户名密码认证

## 最佳实践

1. **使用 curl_cffi**: 安装并使用 curl_cffi 获得最佳效果
2. **保持 User-Agent 一致**: 确保验证请求的 User-Agent 与浏览器一致
3. **完整的 Cookie 集合**: 不仅仅是 cf_clearance，还要包含其他相关 cookies
4. **合理的请求间隔**: 避免过于频繁的验证请求
5. **错误处理**: 妥善处理网络异常和验证失败的情况
