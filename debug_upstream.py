#!/usr/bin/env python3
"""
调试上游代理的响应
"""

import asyncio
import base64

async def debug_upstream_response():
    """调试上游代理的响应"""
    
    # 连接到上游代理
    reader, writer = await asyncio.open_connection('gw.dataimpulse.com', 19965)
    
    try:
        # 构建HTTP请求
        request = "GET http://httpbin.org/ip HTTP/1.1\r\n"
        
        # 添加认证头
        auth_string = "0465846b31e1f583b17c__cr.us:16af34bf75f0573a"
        auth_bytes = auth_string.encode('utf-8')
        auth_b64 = base64.b64encode(auth_bytes).decode('ascii')
        request += f"Proxy-Authorization: Basic {auth_b64}\r\n"
        
        # 添加其他必要头部
        request += "Host: httpbin.org\r\n"
        request += "User-Agent: Python-Debug/1.0\r\n"
        request += "Connection: close\r\n"
        request += "\r\n"
        
        print("发送请求:")
        print(request)
        print("=" * 50)
        
        # 发送请求
        writer.write(request.encode())
        await writer.drain()
        
        # 读取响应
        print("接收响应:")
        response_data = b''
        while True:
            try:
                data = await asyncio.wait_for(reader.read(4096), timeout=5.0)
                if not data:
                    break
                response_data += data
                print(data.decode('utf-8', 'ignore'), end='')
            except asyncio.TimeoutError:
                print("\n[超时，停止读取]")
                break
        
        print("\n" + "=" * 50)
        print(f"总共接收到 {len(response_data)} 字节")
        
    finally:
        writer.close()
        await writer.wait_closed()

if __name__ == "__main__":
    asyncio.run(debug_upstream_response())
